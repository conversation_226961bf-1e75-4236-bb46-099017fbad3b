# KgAdFloat 碰撞检测实现修改说明

## 修改概述

将 kgadfloat 组件从基于占位容器计算 top 限制 move 容器的实现，调整为实时检测广告浮窗是否遮挡指定容器的实现。

## 主要修改内容

### 1. 新增碰撞检测 Hook (`useAdFloatCollisionDetection`)

**文件**: `src/components/_pages/ad-extension/ad/_utils/float.js`

- 实时获取需要避免遮挡的容器位置信息
- 提供碰撞检测函数 `checkCollision`
- 支持多个选择器同时检测
- 定时更新容器位置（处理动态内容变化）

### 2. 增强自动吸附功能 (`useAutoAdsorption`)

**文件**: `src/components/_pages/ad-extension/ad/_utils/float.js`

- 接收碰撞检测参数
- 在拖拽过程中实时检测碰撞
- 在吸附时寻找不碰撞的位置
- 优先尝试左右两侧，然后尝试上下移动

### 3. 新增智能位置调整 Hook (`useSmartPositioning`)

**文件**: `src/components/_pages/ad-extension/ad/_utils/float.js`

- 当检测到碰撞时自动调整广告位置
- 提供多种备选位置策略
- 返回调整后的坐标供 MovableView 使用

### 4. 简化移动区域样式 (`useMoveAreaStyles`)

**文件**: `src/components/_pages/ad-extension/ad/_utils/float.js`

- 移除基于占位容器的 top 计算逻辑
- 改为全屏可移动区域
- 简化了实现逻辑

### 5. 更新 KbAdFloat 组件

**文件**: `src/components/_pages/ad-extension/ad/ad-float.jsx`

- 集成碰撞检测功能
- 使用智能位置调整
- 在 MovableView 中应用调整后的位置

### 6. 修改需要避免遮挡的容器

**文件**:
- `src/components/_pages/query/list/list-content.jsx`
- `src/components/_pages/query/list/components/queryList/index.jsx`

- 给 KbExternalAd 组件添加包裹容器
- 使用唯一的 ID 标识：`kb-ad-avoid-container-1` 和 `kb-ad-avoid-container-2`
- 移除原有的 `float-pos-rb` 占位标识

### 7. 更新页面配置

**文件**: `src/pages/query/index.jsx`

- 简化 selector 配置，使用逗号分隔的ID选择器
- 配置为：`#kb-ad-avoid-container-1,#kb-ad-avoid-container-2`

### 8. 添加样式支持

**文件**: `src/components/_pages/ad-extension/ad/ad-float.scss`

- 为避免遮挡的容器添加ID样式选择器
- 预留调试样式接口

## 技术特点

### 实时检测
- 监听 `active` 属性变化来响应页面滚动
- 在拖拽过程中强制实时更新容器位置并检测碰撞
- 移除了定时器，避免不必要的性能消耗

### 智能避让
- 多种位置策略：左右两侧、上下移动、四个角落
- 优先保持用户拖拽的 Y 坐标
- 如果所有位置都碰撞，保持原始位置

### 性能优化
- 使用防抖函数减少频繁计算
- 拖拽时使用强制更新，确保实时性
- 缓存容器位置信息

### 简化配置
- 使用单一的CSS选择器字符串，支持逗号分隔多个ID
- 使用ID选择器而不是class，提高选择器性能
- 不影响其他页面的 KbAdFloat 使用

## 使用方式

```jsx
<KbAdFloat
  active={adFloatActive}
  position='5'
  placement='rb'
  closeable
  selector='.kb-query>>>#kb-ad-avoid-container-1,.kb-query>>>#kb-ad-avoid-container-2'
/>
```

## 调试信息

为了便于调试，添加了关键事件的控制台日志：

- `🎯 [KbAdFloat]` - 组件激活信息（仅在首次激活时）
- `🚀 [AdFloat]` - 碰撞检测状态变化（active变化时）
- `🔍 [AdFloat]` - 容器位置查询（滚动或初始化时）
- `📦 [AdFloat]` - 容器位置结果
- `🖱️ [AdFloat]` - 拖拽移动事件
- `💥 [AdFloat]` - 检测到碰撞（仅在真正碰撞时）
- `🏁 [AdFloat]` - 触摸结束事件
- `✅ [AdFloat]` - 最终位置计算

## 注意事项

1. 需要避免遮挡的容器应该使用唯一的ID标识
2. selector 必须使用微信小程序的深度查询语法 `.parent>>>#child`
3. 支持逗号分隔多个深度查询选择器
4. 碰撞检测基于矩形相交算法
5. 拖拽时会强制更新容器位置，确保实时检测准确性
6. 通过监听 `active` 变化响应页面滚动，无需定时器
7. 调试日志只在关键事件时触发，避免控制台刷屏
