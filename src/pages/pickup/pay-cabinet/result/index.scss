/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-cabinetPay {
  &-wrapper {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: $spacing-v-md;
  }
  &-result {
    box-sizing: border-box;
    height: 100%;
    padding: 60px $spacing-v-md;
    background-color: $color-white;
    border-radius: $border-radius-md;

    .kb-directory {
      width: max-content;
      margin: 0 auto;
      margin-top: $spacing-h-sm;
      padding: $spacing-h-sm $spacing-h-md;
      color: $color-brand;
      font-size: $font-size-lg;
      background-color: #e5f7f3;
      border-radius: 14px;
      &__img {
        width: 60px;
        height: 48px;
        &-revert {
          transform: rotate(180deg);
        }
      }
    }
    .kb-number {
      color: $color-brand;
      font-weight: bold;
      font-size: 72px;
    }
    .kb-margin-60 {
      margin-top: 60px;
      margin-bottom: 60px;
    }
    .btn {
      width: 288px;
      height: 84px;
      margin: 0 auto;
      color: $color-black-1;
      line-height: 84px;
      text-align: center;
      background-color: $color-grey-5;
      border-radius: $border-radius-md;
    }
    .kb-size__48 {
      font-size: 48px;
    }
    .avatar-img {
      width: 76px;
      height: 76px;
      margin-top: $spacing-h-md;
      border-radius: $border-radius-circle;
    }
    .kb-result-ad {
      width: 100%;
    }
  }
}
