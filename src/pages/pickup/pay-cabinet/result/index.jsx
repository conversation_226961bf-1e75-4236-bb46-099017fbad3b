/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbPage from '~base/components/page';
import { Image, Text, View } from '@tarojs/components';
import { padZero } from '~/components/_pages/kdg/_utils';
import KbExternalAd from '~/components/_pages/ad-extension/ad';
import { frequencyLimit, reportAnalytics } from '~base/utils/utils';
import { getAdConfig } from '~/components/_pages/ad-extension/_utils';
import { adNavigator } from '~/components/_pages/ad-extension/sdk';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '支付成功',
  };

  state = {
    data: {},
    ad: null,
  };

  componentDidMount() {
    const data = Taro.kbGetGlobalDataOnce('cabinetPayInfo');
    this.setState({
      data,
    });

    const limit = frequencyLimit('check', 'cabinet_pay_success');
    if (!limit) {
      getAdConfig({ position: '2' }).then((res) => {
        if (res[0]) {
          this.setState({
            ad: res[0],
          });
          frequencyLimit('limit', 'cabinet_pay_success');
          reportAnalytics({
            key: 'event_push_ad',
            title: '快递柜取件支付成功',
            status: 'load',
          });
        }
      });
    } else {
      reportAnalytics({
        key: 'event_push_ad',
        title: '快递柜取件支付成功',
        status: 'load-fail-daily',
      });
    }
  }

  handleClick = () => {
    const { ad } = this.state;
    if (ad) {
      reportAnalytics({
        key: 'event_push_ad',
        title: '快递柜取件支付成功',
        status: 'click',
      });
      adNavigator({
        ...ad,
        force: true,
      });
    } else {
      Taro.navigator({
        url: 'query',
        target: 'tabs',
        force: true,
      });
    }
  };

  render() {
    const { data, ad, ...rest } = this.state;
    const { in_type, direction, number, grid_number, row, col } = data || {};

    return (
      <KbPage {...rest}>
        <View className='kb-cabinetPay-wrapper'>
          <View className='kb-cabinetPay-result'>
            <View className='at-row at-row__direction--column at-row__align--center'>
              {direction ? (
                <View className='kb-directory at-row at-row__align--center'>
                  {direction == 'left' && (
                    <Image
                      className='kb-directory__img kb-directory__img-revert kb-margin-md-r'
                      src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
                    />
                  )}
                  {(direction == 'left' || direction == 'right') && (
                    <View className='kb-size__lg'>
                      显示屏{direction == 'left' ? '左侧' : '右侧'}
                    </View>
                  )}
                  {direction == 'right' && (
                    <Image
                      className='kb-directory__img kb-margin-md-l'
                      src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
                    />
                  )}
                </View>
              ) : (
                <View className='at-row at-row__justify--center'>
                  <Image
                    className='avatar-img'
                    src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2023/06/01/6478142d5caae/<EMAIL>'
                  />
                </View>
              )}
              {in_type == 'shelf' ? (
                <Fragment>
                  <View className='kb-text__center kb-spacing-xl-t kb-size__48 kb-color__brand'>
                    支付完成
                  </View>
                  <View className='kb-margin-60' />
                </Fragment>
              ) : (
                <Fragment>
                  <View className='kb-color__black kb-spacing-md-tb'>
                    {number && grid_number == 1 ? (
                      <Text className='kb-size__48'>
                        （格口
                        <Text className='kb-number'>{number}</Text> ）
                      </Text>
                    ) : (
                      <View className='kb-size__48 at-row at-row__align--baseline at-row__justify--center'>
                        <Text className='kb-number'>{padZero(row)}</Text>
                        号柜 <Text className='kb-number'>{padZero(col)}</Text>
                        格口
                      </View>
                    )}
                  </View>

                  <View className='kb-color__grey kb-size__lg'>柜门已开，请取走包裹并关上柜门</View>
                </Fragment>
              )}
              <View className='kb-margin-60 kb-spacing-md-b kb-result-ad'>
                <KbExternalAd wrapper adUnitIdIndex='cabinet_pay' force />
              </View>
            </View>
            <View className='btn' hoverClass='kb-hover-opacity' onClick={this.handleClick}>
              {ad ? ad.title : '完成'}
            </View>
          </View>
        </View>
      </KbPage>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
