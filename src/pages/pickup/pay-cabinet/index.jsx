/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, createRef } from '@tarojs/taro';
import KbPage from '~base/components/page';
import { Image, View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import {
  getBatchPaySign,
  getStoragePaySign,
} from '~/components/_pages/kdg/cabinetOpen/_utils/utils';
import { getAdConfig } from '~/components/_pages/ad-extension/_utils';
import { requestPayment } from '~/utils/qy';
import { scanAction } from '~/utils/scan';
import request from '~base/utils/request';
import { reportAnalytics } from '~base/utils/utils';
import { adNavigator } from '~/components/_pages/ad-extension/sdk';
import { connect } from '@tarojs/redux';
import qs from 'qs';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '支付超时费',
  };

  constructor() {
    super();
    this.state = {
      data: {},
      ad: null,
      ip: '',
    };
    this.cabinetRef = createRef();
    this.cabinetRef.current = {};
  }

  handleUpdate = (data) => {
    if (data.logined) {
      this.setState({
        loginData: data,
      });
      scanAction(null, this)
        .then((res) => {
          console.log('scanAction------', res);
          this.init(res);
        })
        .catch(() => {
          const { params } = this.$router;
          if (params && params.waybill_no) {
            const p = qs.stringify(params);
            const q = decodeURIComponent(decodeURIComponent(p));
            this.init(qs.parse(q));
          }
        });
      this.getIp();
    }
  };

  componentDidShow() {
    const data = Taro.kbGetGlobalDataOnce('za-result');
    console.log(data, '-----onShow');
    console.log(this.state.data, '----data');
    if (data) {
      Taro.navigateToMiniProgram({
        appId: data.redirect_appid,
        path: data.redirect_path,
        extraData: data,
        success: (res) => {
          console.log('to sign-success', res);
        },
        fail: (err) => {
          console.log('to sign-fail', err);
          Taro.kbModal({
            top: false,
            title: '温馨提示',
            content: [{ className: 'kb-text__center', text: '网络异常，请重试！' }],
            confirmText: '立即开通',
            onConfirm: () => {
              Taro.navigateToMiniProgram({
                appId: data.redirect_appid,
                path: data.redirect_path,
                extraData: data,
              });
            },
          });
        },
      });
      // Taro.navigator({
      //   url: data.redirect_path,
      //   appId: data.redirect_appid,
      //   extraData: data,
      //   force: true,
      // });
      return;
    }

    if (this.jumpAd) {
      Taro.kbToast({
        text: '请确认是否开通赠险权益免费取件，未开通需支付超时费后开柜取件',
      });
      this.checkStatus();
    }
  }

  checkStatus = () => {
    console.log('--------check-------');
    request({
      url: '/api/weixin/mini/DakMini/Record/getPayInfo',
      toastLoading: false,
      toastError: false,
      mastHasMobile: false,
      data: {
        out_trade_no: this.state.data.out_trade_no,
      },
      onThen: (res) => {
        console.log('pay-result-----', res);
        if (res.code == 0 && res.data) {
          const { pay_status } = res.data;
          if (pay_status > 0) {
            Taro.navigator({
              url: 'pickup/pay-cabinet/result',
              target: 'self',
              force: true,
            });
          } else {
            setTimeout(() => {
              this.checkStatus();
            }, 1000);
          }
        }
      },
    });
  };

  init = (data) => {
    const parsedParams = {
      ...data,
      token: data.token.replace(/ /g, '+'), // 处理加号
    };
    console.log('parsedParams------', parsedParams);
    this.getInfo(parsedParams);
    this.setState({
      data: parsedParams,
    });
  };

  getInfo = (data) => {
    const { ec_id, waybill_no } = data;
    if (!waybill_no) return;
    request({
      url: '/api/weixin/mini/DakMini/Grid/getGridInfo',
      toastLoading: false,
      mastHasMobile: false,
      data: {
        ec_id,
        waybill_no,
      },
      onThen: (res) => {
        process.env.NODE_ENV === 'development' && this.loadAd();
        if (res.code == 0) {
          Taro.kbSetGlobalData('cabinetPayInfo', {
            ...data,
            ...(res.data || {}),
          });
          this.setState(
            {
              data: {
                ...data,
                ...(res.data || {}),
              },
            },
            () => {
              this.loadAd();
            },
          );
        }
      },
    });
  };

  handlePay = () => {
    const { in_type, ...restData } = this.state.data;
    let api, params;
    if (in_type == 'storage') {
      api = getStoragePaySign;
      params = {
        order_id: restData.waybill_no,
      };
    } else {
      api = getBatchPaySign;
      params = {
        ...restData,
        pay_method: 'wechat',
      };
    }

    api(params).then((res) => {
      if (res.code == 0) {
        requestPayment(res.data)
          .then(() => {
            Taro.kbToast({ text: '支付成功' });
            setTimeout(() => {
              Taro.navigator({
                url: 'pickup/pay-cabinet/result',
                target: 'self',
                force: true,
              });
            }, 1000);
          })
          .catch((err) => {
            console.log(err);
            Taro.kbToast({ text: '支付失败' });
          });
      } else {
        Taro.kbToast({ text: res.msg });
      }
    });
  };

  loadAd = () => {
    // Tower 任务: 0424_快递柜-对接众安保险合作 ( https://tower.im/teams/258300/todos/113378 )
    const {
      data: { money, in_type },
    } = this.state;
    if (in_type) return;
    getAdConfig({ position: '3' }).then(async (res) => {
      const ad = res[0] || {};
      const url = ad.adUrl;
      if (res[0] && money <= 5) {
        const result = await this.checkCanShowAd(url);
        if (result.code > 0) {
          this.setState({
            ad,
          });
          reportAnalytics({
            key: 'event_push_ad',
            title: '快递柜取件超时支付广告',
            status: 'load',
          });
        } else {
          reportAnalytics({
            key: 'event_push_ad',
            title: '快递柜取件超时支付广告',
            status: 'load-fail-loadedAd',
          });
        }
      } else {
        reportAnalytics({
          key: 'event_push_ad',
          title: '快递柜取件超时支付广告',
          status: 'load-fail',
        });
      }
    });
  };

  checkCanShowAd = (url) => {
    return new Promise((resolve) => {
      request({
        url: '/api/weixin/mini/DakMini/Record/getUserFreeOrder',
        toastLoading: false,
        mastHasMobile: false,
        data: {
          url,
        },
        onThen: resolve,
      });
    });
  };

  handleClickAd = () => {
    const { out_trade_no, money } = this.state.data;
    request({
      url: '/api/weixin/mini/DakMini/Record/setZhongAnAdFreeOvertime',
      toastError: true,
      mastHasMobile: false,
      data: {
        out_trade_no,
        money,
      },
      onThen: (res) => {
        if (res.code == 0) {
          this.jumpAd = true;
          reportAnalytics({
            key: 'event_push_ad',
            title: '快递柜取件超时支付广告',
            status: 'click',
          });
          const { loginData } = this.state;
          const { userInfo: { openid } = {} } = loginData || {};
          console.log(this.props, '----', loginData, openid);
          const { ad, ip } = this.state;
          let { adUrl, ...rest } = ad || {};
          const params = `bizContent=kb_${out_trade_no}&ip=${ip}&appId=wx1cc70b43e2af1811&openId=${openid}`;
          adUrl = adUrl.indexOf('?') > -1 ? `${adUrl}&${params}` : `${adUrl}?${params}`;
          console.log(adUrl);
          adNavigator({
            ...rest,
            adUrl,
            force: true,
          });
        }
      },
    });
  };

  getIp = () => {
    request({
      url: '/api/weixin/mini/DakMini/Record/getUserIp',
      toastLoading: false,
      mastHasMobile: false,
      onThen: (res) => {
        if (res.code == 0 && res.data) {
          this.setState({
            ip: res.data.id,
          });
        }
      },
    });
  };

  render() {
    const { data, ad, ...rest } = this.state;
    const { courier_name, money } = data;

    return (
      <KbPage {...rest} onUpdate={this.handleUpdate}>
        <View className='kb-cabinetPay-wrapper'>
          <View className='kb-cabinetPay'>
            <View className='at-row at-row__align--center'>
              <Image
                className='kb-cabinetPay-avatar'
                src='https://cdn-img.kuaidihelp.com/m/yzAppDefaultAvatar.png'
              />
              <View>
                <View className='kb-color__black kb-margin-md-b'>收款方：{courier_name}</View>
                <View className='kb-color__grey kb-size__sm'>
                  包裹超时存放保管费，其他用途都是诈骗！
                </View>
              </View>
            </View>
            <View className='kb-margin-90'>应付金额：</View>
            <View className='kb-cabinetPay-money kb-border-b'>￥{money}</View>
            <AtButton className='kb-cabinetPay-btn' type='secondary' onClick={this.handlePay}>
              立即支付
            </AtButton>
            {ad && (
              <View
                className='kb-cabinetPay-ad'
                hoverClass='kb-hover-opacity'
                onClick={this.handleClickAd}
              >
                <View>{ad.title}</View>
                <View className='kb-ad-desc'>{ad.remark}</View>
              </View>
            )}
          </View>
        </View>
      </KbPage>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
