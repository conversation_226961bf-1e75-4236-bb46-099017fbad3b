/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

 .kb-ad-float {
  top: auto;
  left: auto;
  z-index: 9999;
  width: auto;
  height: auto;
  pointer-events: initial;
  max-width: 200px !important;
  max-height: 200px !important;

  &__area {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: auto;
    margin-bottom: 0;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    pointer-events: none;
  }

  &__image {
    max-width: 200px !important;
    max-height: 200px !important;
    vertical-align: middle;
  }

  &__close {
    position: absolute;
    top: -10px;
    right: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    background-color: #999;
    border-radius: 50%;

    .kb-icon {
      color: $color-white !important;
      font-size: $font-size-sm/2 !important;
    }
  }

  // &__guid {
  //   display: flex;
  //   max-width: 200px !important;
  //   max-height: 200px !important;
  // }

  &__placement {
    &--rt,
    &--rb,
    &--rb2 {
      right: 0;
    }

    &--lt,
    &--lb {
      left: 0;
    }

    &--lt,
    &--rt {
      top: 360px;
    }

    &--rb,
    &--lb {
      bottom: 360px;
    }

    &--rb2 {
      bottom: 300px;
    }

    &--rb,
    &--lb,
    &--rb2 {
      margin-bottom: 0;
      margin-bottom: constant(safe-area-inset-bottom);
      margin-bottom: env(safe-area-inset-bottom);
    }
  }

  &__share {
    &--btn {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: transparent;

      &::after {
        display: none;
      }
    }
  }
}
