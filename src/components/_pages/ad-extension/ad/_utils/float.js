import { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附，支持避免遮挡检测
 */
export function useAutoAdsorption(collisionDetection) {
  const ref = useRef({ curX: 0, curY: 0, imageSize: { width: 0, height: 0 } }); // 移动位置以及图片尺寸
  const [posX, setPosX] = useState();
  const [posY, setPosy] = useState();

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;

    console.log('🖱️ [AdFloat] 拖拽移动:', { x, y });

    // 实时更新容器位置并检测碰撞
    if (collisionDetection && collisionDetection.forceUpdateContainers) {
      console.log('🔄 [AdFloat] 触发强制更新容器位置');
      collisionDetection.forceUpdateContainers();

      // 延迟一点检测碰撞，确保容器位置已更新
      setTimeout(() => {
        if (collisionDetection.checkCollision) {
          const { width, height } = ref.current.imageSize;
          const adFloatRect = {
            left: x,
            top: y,
            right: x + width,
            bottom: y + height,
          };

          console.log('🎯 [AdFloat] 拖拽时检测碰撞:', adFloatRect);
          // 检测碰撞但暂时不做处理，可以在这里添加视觉反馈
          collisionDetection.checkCollision(adFloatRect);
        }
      }, 50);
    } else {
      console.log('❌ [AdFloat] 碰撞检测对象不可用');
    }
  };

  // 寻找不遮挡的位置
  const findNonCollidingPosition = (targetX, targetY) => {
    if (!collisionDetection || !collisionDetection.checkCollision) {
      return { x: targetX, y: targetY };
    }

    const { width, height } = ref.current.imageSize;
    const { windowWidth: screenWidth, windowHeight: screenHeight } = getSystemInfoSync();

    // 检测目标位置是否碰撞
    const targetRect = {
      left: targetX,
      top: targetY,
      right: targetX + width,
      bottom: targetY + height,
    };

    if (!collisionDetection.checkCollision(targetRect)) {
      return { x: targetX, y: targetY };
    }

    // 如果碰撞，尝试寻找其他位置
    const positions = [
      // 优先尝试左右两侧的不同高度
      { x: 0, y: targetY },
      { x: screenWidth - width, y: targetY },
      { x: 0, y: targetY - 100 },
      { x: screenWidth - width, y: targetY - 100 },
      { x: 0, y: targetY + 100 },
      { x: screenWidth - width, y: targetY + 100 },
      // 如果还是碰撞，尝试更多位置
      { x: 0, y: 100 },
      { x: screenWidth - width, y: 100 },
      { x: 0, y: screenHeight - height - 100 },
      { x: screenWidth - width, y: screenHeight - height - 100 },
    ];

    for (const pos of positions) {
      const testRect = {
        left: pos.x,
        top: pos.y,
        right: pos.x + width,
        bottom: pos.y + height,
      };

      if (!collisionDetection.checkCollision(testRect)) {
        return pos;
      }
    }

    // 如果所有位置都碰撞，返回原始目标位置
    return { x: targetX, y: targetY };
  };

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线

    console.log('🏁 [AdFloat] 触摸结束:', { curX, curY, threshold });

    // 判断靠左还是靠右
    setPosX(curX);
    setPosy(curY);

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      const targetX = Math.abs(curX) + imgWidth / 2 < threshold ? 0 : screenWidth - imgWidth;

      console.log('🎯 [AdFloat] 计算目标位置:', { targetX, curY });

      // 寻找不碰撞的位置
      const finalPosition = findNonCollidingPosition(targetX, curY);
      console.log('✅ [AdFloat] 最终位置:', finalPosition);

      setPosX(finalPosition.x);
      setPosy(finalPosition.y);
    }, 100);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  useEffect(() => {
    if (!selector || !active) return;
    setPosReady(true);
  }, [active, selector]);

  // moveArea样式 - 全屏可移动区域
  const styles = useMemo(() => {
    return {
      top: `${top + height}px`,
      bottom: isTabPage ? '100rpx' : 0
    };
  }, [top, height, isTabPage]);

  return {
    posReady,
    styles,
  };
}

/**
 *
 * @description 实时检测广告浮窗是否遮挡指定容器
 */
export function useAdFloatCollisionDetection(props) {
  const { selector, active = true } = props;
  const [avoidContainers, setAvoidContainers] = useState([]);

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    console.log('🔍 [AdFloat] 获取容器位置结果:', res);
    if (Array.isArray(res)) {
      const validContainers = res.filter(item => item && item.width > 0 && item.height > 0);
      console.log('📦 [AdFloat] 有效容器数量:', validContainers.length, validContainers);
      setAvoidContainers(validContainers);
    } else if (res && res.width > 0 && res.height > 0) {
      console.log('📦 [AdFloat] 单个有效容器:', res);
      setAvoidContainers([res]);
    } else {
      console.log('❌ [AdFloat] 未找到有效容器');
      setAvoidContainers([]);
    }
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 100, { trailing: true, leading: false }),
    [],
  );

  // 检测碰撞的函数
  const checkCollision = useCallback((adFloatRect) => {
    if (!adFloatRect || avoidContainers.length === 0) {
      console.log('⚠️ [AdFloat] 碰撞检测跳过:', {
        hasRect: !!adFloatRect,
        containerCount: avoidContainers.length
      });
      return false;
    }

    const hasCollision = avoidContainers.some(container => {
      // 检测矩形是否相交
      const isColliding = !(
        adFloatRect.right < container.left ||
        adFloatRect.left > container.right ||
        adFloatRect.bottom < container.top ||
        adFloatRect.top > container.bottom
      );

      if (isColliding) {
        console.log('💥 [AdFloat] 检测到碰撞!', {
          adFloat: adFloatRect,
          container: container
        });
      }

      return isColliding;
    });

    console.log('🎯 [AdFloat] 碰撞检测结果:', hasCollision);
    return hasCollision;
  }, [avoidContainers]);

  // 获取需要避免遮挡的容器位置
  const updateAvoidContainers = useCallback(() => {
    if (!selector || !active) {
      console.log('⚠️ [AdFloat] 跳过容器更新:', { selector, active });
      return;
    }

    // 支持CSS选择器字符串（逗号分隔）或数组
    let selectors = [];
    if (typeof selector === 'string') {
      // 如果包含逗号，说明是多个选择器
      selectors = selector.includes(',')
        ? selector.split(',').map(s => s.trim())
        : [selector];
    } else if (Array.isArray(selector)) {
      selectors = selector;
    } else {
      selectors = [selector];
    }

    console.log('🔍 [AdFloat] 开始查询容器位置:', selectors);

    const selectorConfigs = selectors.map(sel => ({
      selector: sel,
      component: false
    }));

    triggerGetBoundingDebounce(selectorConfigs);
  }, [selector, active, triggerGetBoundingDebounce]);

  // 初始化和监听滚动事件
  useEffect(() => {
    console.log('🚀 [AdFloat] 碰撞检测初始化:', { active, selector });

    if (!active) {
      console.log('❌ [AdFloat] 组件未激活，跳过初始化');
      return;
    }

    console.log('✅ [AdFloat] 开始初始化容器检测');
    updateAvoidContainers();

    // 更频繁的定时更新容器位置（处理动态内容变化和滚动）
    const intervalUpdate = setInterval(() => {
      console.log('⏰ [AdFloat] 定时更新容器位置');
      updateAvoidContainers();
    }, 500); // 减少到500ms，提高响应性

    return () => {
      console.log('🧹 [AdFloat] 清理定时器');
      clearInterval(intervalUpdate);
    };
  }, [active, selector, updateAvoidContainers]);

  // 提供手动更新方法，供拖拽时调用
  const forceUpdateContainers = useCallback(() => {
    if (!selector || !active) {
      console.log('⚠️ [AdFloat] 跳过强制更新:', { selector, active });
      return;
    }

    let selectors = [];
    if (typeof selector === 'string') {
      selectors = selector.includes(',')
        ? selector.split(',').map(s => s.trim())
        : [selector];
    } else if (Array.isArray(selector)) {
      selectors = selector;
    } else {
      selectors = [selector];
    }

    console.log('🚀 [AdFloat] 强制更新容器位置:', selectors);

    const selectorConfigs = selectors.map(sel => ({
      selector: sel,
      component: false
    }));

    // 立即执行，不使用防抖
    triggerGetBounding(selectorConfigs);
  }, [selector, active, triggerGetBounding]);

  return {
    checkCollision,
    updateAvoidContainers,
    forceUpdateContainers,
    avoidContainers,
  };
}

/**
 *
 * @description 智能位置调整，当检测到碰撞时自动调整广告位置
 */
export function useSmartPositioning(collisionDetection, posX, posY, imageSize) {
  const [adjustedPosX, setAdjustedPosX] = useState(posX);
  const [adjustedPosY, setAdjustedPosY] = useState(posY);

  useEffect(() => {
    if (!collisionDetection || !collisionDetection.checkCollision || !imageSize.width || !imageSize.height) {
      setAdjustedPosX(posX);
      setAdjustedPosY(posY);
      return;
    }

    const currentRect = {
      left: posX || 0,
      top: posY || 0,
      right: (posX || 0) + imageSize.width,
      bottom: (posY || 0) + imageSize.height,
    };

    // 如果当前位置没有碰撞，直接使用
    if (!collisionDetection.checkCollision(currentRect)) {
      setAdjustedPosX(posX);
      setAdjustedPosY(posY);
      return;
    }

    // 如果有碰撞，寻找合适的位置
    const { windowWidth: screenWidth, windowHeight: screenHeight } = getSystemInfoSync();
    const { width, height } = imageSize;

    const positions = [
      // 优先尝试当前Y位置的左右两侧
      { x: 0, y: posY || 0 },
      { x: screenWidth - width, y: posY || 0 },
      // 尝试上下移动
      { x: posX || 0, y: Math.max(0, (posY || 0) - 150) },
      { x: posX || 0, y: Math.min(screenHeight - height, (posY || 0) + 150) },
      // 尝试四个角落
      { x: 0, y: 100 },
      { x: screenWidth - width, y: 100 },
      { x: 0, y: screenHeight - height - 100 },
      { x: screenWidth - width, y: screenHeight - height - 100 },
    ];

    for (const pos of positions) {
      const testRect = {
        left: pos.x,
        top: pos.y,
        right: pos.x + width,
        bottom: pos.y + height,
      };

      if (!collisionDetection.checkCollision(testRect)) {
        setAdjustedPosX(pos.x);
        setAdjustedPosY(pos.y);
        return;
      }
    }

    // 如果所有位置都碰撞，使用原始位置
    setAdjustedPosX(posX);
    setAdjustedPosY(posY);
  }, [collisionDetection, posX, posY, imageSize]);

  return {
    adjustedPosX,
    adjustedPosY,
  };
}
