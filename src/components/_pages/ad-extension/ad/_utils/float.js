import { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附，支持避免遮挡检测
 */
export function useAutoAdsorption(collisionDetection) {
  const ref = useRef({ curX: 0, curY: 0, imageSize: { width: 0, height: 0 } }); // 移动位置以及图片尺寸
  const [posX, setPosX] = useState();
  const [posY, setPosy] = useState();

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;

    // 实时检测碰撞
    if (collisionDetection && collisionDetection.checkCollision) {
      const { width, height } = ref.current.imageSize;
      const adFloatRect = {
        left: x,
        top: y,
        right: x + width,
        bottom: y + height,
      };

      // 检测碰撞但暂时不做处理，可以在这里添加视觉反馈
      collisionDetection.checkCollision(adFloatRect);
    }
  };

  // 寻找不遮挡的位置
  const findNonCollidingPosition = (targetX, targetY) => {
    if (!collisionDetection || !collisionDetection.checkCollision) {
      return { x: targetX, y: targetY };
    }

    const { width, height } = ref.current.imageSize;
    const { windowWidth: screenWidth, windowHeight: screenHeight } = getSystemInfoSync();

    // 检测目标位置是否碰撞
    const targetRect = {
      left: targetX,
      top: targetY,
      right: targetX + width,
      bottom: targetY + height,
    };

    if (!collisionDetection.checkCollision(targetRect)) {
      return { x: targetX, y: targetY };
    }

    // 如果碰撞，尝试寻找其他位置
    const positions = [
      // 优先尝试左右两侧的不同高度
      { x: 0, y: targetY },
      { x: screenWidth - width, y: targetY },
      { x: 0, y: targetY - 100 },
      { x: screenWidth - width, y: targetY - 100 },
      { x: 0, y: targetY + 100 },
      { x: screenWidth - width, y: targetY + 100 },
      // 如果还是碰撞，尝试更多位置
      { x: 0, y: 100 },
      { x: screenWidth - width, y: 100 },
      { x: 0, y: screenHeight - height - 100 },
      { x: screenWidth - width, y: screenHeight - height - 100 },
    ];

    for (const pos of positions) {
      const testRect = {
        left: pos.x,
        top: pos.y,
        right: pos.x + width,
        bottom: pos.y + height,
      };

      if (!collisionDetection.checkCollision(testRect)) {
        return pos;
      }
    }

    // 如果所有位置都碰撞，返回原始目标位置
    return { x: targetX, y: targetY };
  };

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线

    // 判断靠左还是靠右
    setPosX(curX);
    setPosy(curY);

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      const targetX = Math.abs(curX) + imgWidth / 2 < threshold ? 0 : screenWidth - imgWidth;

      // 寻找不碰撞的位置
      const finalPosition = findNonCollidingPosition(targetX, curY);
      setPosX(finalPosition.x);
      setPosy(finalPosition.y);
    }, 100);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  useEffect(() => {
    if (!selector || !active) return;
    setPosReady(true);
  }, [active, selector]);

  // moveArea样式 - 全屏可移动区域
  const styles = useMemo(() => {
    return {
      top: `${top + height}px`,
      bottom: isTabPage ? '100rpx' : 0
    };
  }, [top, height, isTabPage]);

  return {
    posReady,
    styles,
  };
}

/**
 *
 * @description 实时检测广告浮窗是否遮挡指定容器
 */
export function useAdFloatCollisionDetection(props) {
  const { selector, active = true } = props;
  const [avoidContainers, setAvoidContainers] = useState([]);

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    if (Array.isArray(res)) {
      setAvoidContainers(res.filter(item => item && item.width > 0 && item.height > 0));
    } else if (res && res.width > 0 && res.height > 0) {
      setAvoidContainers([res]);
    }
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 100, { trailing: true, leading: false }),
    [],
  );

  // 检测碰撞的函数
  const checkCollision = useCallback((adFloatRect) => {
    if (!adFloatRect || avoidContainers.length === 0) return false;

    return avoidContainers.some(container => {
      // 检测矩形是否相交
      return !(
        adFloatRect.right < container.left ||
        adFloatRect.left > container.right ||
        adFloatRect.bottom < container.top ||
        adFloatRect.top > container.bottom
      );
    });
  }, [avoidContainers]);

  // 获取需要避免遮挡的容器位置
  const updateAvoidContainers = useCallback(() => {
    if (!selector || !active) return;

    // 支持多个选择器
    const selectors = Array.isArray(selector) ? selector : [selector];
    const selectorConfigs = selectors.map(sel => ({
      selector: sel,
      component: false
    }));

    triggerGetBoundingDebounce(selectorConfigs);
  }, [selector, active, triggerGetBoundingDebounce]);

  // 初始化和监听滚动事件
  useEffect(() => {
    if (!active) return;

    updateAvoidContainers();

    // 定时更新容器位置（处理动态内容变化和滚动）
    const intervalUpdate = setInterval(() => {
      updateAvoidContainers();
    }, 1000);

    return () => {
      clearInterval(intervalUpdate);
    };
  }, [active, updateAvoidContainers]);

  return {
    checkCollision,
    updateAvoidContainers,
    avoidContainers,
  };
}

/**
 *
 * @description 智能位置调整，当检测到碰撞时自动调整广告位置
 */
export function useSmartPositioning(collisionDetection, posX, posY, imageSize) {
  const [adjustedPosX, setAdjustedPosX] = useState(posX);
  const [adjustedPosY, setAdjustedPosY] = useState(posY);

  useEffect(() => {
    if (!collisionDetection || !collisionDetection.checkCollision || !imageSize.width || !imageSize.height) {
      setAdjustedPosX(posX);
      setAdjustedPosY(posY);
      return;
    }

    const currentRect = {
      left: posX || 0,
      top: posY || 0,
      right: (posX || 0) + imageSize.width,
      bottom: (posY || 0) + imageSize.height,
    };

    // 如果当前位置没有碰撞，直接使用
    if (!collisionDetection.checkCollision(currentRect)) {
      setAdjustedPosX(posX);
      setAdjustedPosY(posY);
      return;
    }

    // 如果有碰撞，寻找合适的位置
    const { windowWidth: screenWidth, windowHeight: screenHeight } = getSystemInfoSync();
    const { width, height } = imageSize;

    const positions = [
      // 优先尝试当前Y位置的左右两侧
      { x: 0, y: posY || 0 },
      { x: screenWidth - width, y: posY || 0 },
      // 尝试上下移动
      { x: posX || 0, y: Math.max(0, (posY || 0) - 150) },
      { x: posX || 0, y: Math.min(screenHeight - height, (posY || 0) + 150) },
      // 尝试四个角落
      { x: 0, y: 100 },
      { x: screenWidth - width, y: 100 },
      { x: 0, y: screenHeight - height - 100 },
      { x: screenWidth - width, y: screenHeight - height - 100 },
    ];

    for (const pos of positions) {
      const testRect = {
        left: pos.x,
        top: pos.y,
        right: pos.x + width,
        bottom: pos.y + height,
      };

      if (!collisionDetection.checkCollision(testRect)) {
        setAdjustedPosX(pos.x);
        setAdjustedPosY(pos.y);
        return;
      }
    }

    // 如果所有位置都碰撞，使用原始位置
    setAdjustedPosX(posX);
    setAdjustedPosY(posY);
  }, [collisionDetection, posX, posY, imageSize]);

  return {
    adjustedPosX,
    adjustedPosY,
  };
}
