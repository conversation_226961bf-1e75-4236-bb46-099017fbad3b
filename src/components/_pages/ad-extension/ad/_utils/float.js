import { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附
 */
export function useAutoAdsorption() {
  const ref = useRef({ curX: 0, curY: 0, imageSize: { width: 0, height: 0 } }); // 移动位置以及图片尺寸
  const [posX, setPosX] = useState();
  const [posY, setPosy] = useState();

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;
  };

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线
    // 判断靠左还是靠右，并吸附
    setPosX(curX);
    setPosy(curY);
    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      setPosX(Math.abs(curX) + imgWidth / 2 < threshold ? 0 : 0 - imgWidth - screenWidth);
    }, 100);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);
  const [posPatchTop, setPosPatchTop] = useState(0); // 当有selector时，需要获取selector位置，并设置patchTop

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    const { top: posTop } = res || {};
    setPosReady(true);
    if (posTop) {
      setPosPatchTop(posTop);
    }
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  useEffect(() => {
    if (!selector || !active) return;
    triggerGetBoundingDebounce({ selector, component: false });
  }, [active, selector]);

  // moveArea样式
  const styles = useMemo(() => {
    const { windowHeight: screenHeight } = getSystemInfoSync();
    const _top = `${Math.max(posPatchTop, top + height) > screenHeight - 100 ? top + height : Math.max(posPatchTop, top + height)}px`;

    return { top: _top, bottom: isTabPage ? '100rpx' : 0 };
  }, [posPatchTop, top, height, isTabPage]);

  return {
    posReady,
    styles,
  };
}
