/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 激励视频/插屏广告
// idsMap
export const adIdsMap = {
  0: {
    id: 'adunit-e8c9b02e1e3ccfc4',
    label: '我的更多推荐',
    type: 'rewardedVideo',
  },
  1: {
    id: 'adunit-0b6279a459673f34',
    label: '下单结果页',
    type: 'interstitial',
  },
  welfare: {
    id: 'adunit-dc28a833c9cab0f2',
    label: '福利中心',
    type: 'interstitial',
  },
  lotteryList: {
    id: 'adunit-564efa7d10aa40a7',
    label: '抽奖列表',
    type: 'interstitial',
  },
  lotteryDetails: {
    id: 'adunit-6ccd7d30cde9dca8',
    label: '抽奖详情',
    type: 'interstitial',
  },
  pickup: {
    id: 'adunit-dc3aab5c1e681778',
    label: '预约取件',
    type: 'interstitial',
  },
  lotteryTask: {
    id: 'adunit-26ef0e1c1b8c2498',
    label: '抽奖详情任务',
    type: 'rewardedVideo',
  },
};

export default {
  'query.appointment': 'adunit-b1d212e63f99b9dd.custom', // 预约取件位置：
  'query.detail': 'adunit-3239f196a5fe0bbe.custom', //物流详情位置：
  query: 'adunit-9a8f8ec06b0d1a50.custom', //首页取件位置：
  pickupCode: 'adunit-dbe696eea2f1b035.custom', //取件码取件位置：
  cabinet_pickup: 'adunit-f09fe6800a67ebf2.custom', // 快递柜取件
  cabinet_pay: 'adunit-7f67c87569100645.custom', // 快递柜支付结果
};
