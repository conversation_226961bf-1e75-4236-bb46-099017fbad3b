/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import KbScan from '@base/components/scan';
import { AtInput } from 'taro-ui';

import './index.scss';

const KdgStorageRemark = (props) => {
  const { remark, updatePageData } = props;
  const handelSearch = (v) => {
    v && updatePageData({ remark: v });
  };

  return (
    <View className='kdg_remark'>
      <View className='kdg_remark__title'>物品备注</View>
      <View className='kdg_remark__content'>
        <View className='at-col'>
          <AtInput
            value={remark}
            placeholder='请输入（选填）'
            onChange={(value) => updatePageData({ remark: value })}
          />
        </View>
        <View className='kb-spacing-md-l'>
          <KbScan onChange={handelSearch} />
        </View>
      </View>
    </View>
  );
};

KdgStorageRemark.options = {
  addGlobalClass: true,
};

export default KdgStorageRemark;
