/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro, { useEffect, useState, useRef } from '@tarojs/taro';
import isUndefined from 'lodash/isUndefined';
import { REFRESH_KEY_APPOINTMENT_LIST, refreshControl } from '~/utils/refresh-control';

export const useReject = (props) => {
  const {
    visible,
    // handleContinue,
    // handleCloseSuccess,
    // handleOpenCabinet,
    openSuccessInfo: openSuccessInfoProps,
    brands,
    dispatchGet,
  } = props;

  const [rejectionInfo, setRejectionInfo] = useState({});

  const {
    baseData,
    gridId: grid_id,
    batch_no,
    waybillList = [],
    waybill_no,
    status,
    message,
  } = rejectionInfo || {};

  const [step, setStep] = useState(1);
  const [checked, setChecked] = useState([]);

  const cabinetPickupRef = useRef({});

  useEffect(() => {
    openSuccessInfoProps && setRejectionInfo(openSuccessInfoProps);
  }, [openSuccessInfoProps]);

  const handleContinue = (v) => {
    const fn = props.handleContinue || cabinetPickupRef.current.handleContinue;
    fn(v);
  };
  const handleCloseSuccess = (v) => {
    const fn = props.handleCloseSuccess || cabinetPickupRef.current.handleCloseSuccess;
    fn(v);
  };
  const handleOpenCabinet = (v) => {
    const fn = props.handleOpenCabinet || cabinetPickupRef.current.handleOpenCabinet;
    fn(v);
  };

  const handleChoose = (item) => {
    const _checked = [...checked];
    const index = _checked.findIndex((v) => v.waybill_no == item.waybill_no);
    if (index > -1) {
      _checked.splice(index, 1);
    } else {
      _checked.push(item);
    }
    setChecked(_checked);
  };

  const handleCancel = () => {
    refreshControl(REFRESH_KEY_APPOINTMENT_LIST);
    if (step == 1 && isUndefined(status)) {
      handleOpenCabinet({ ...baseData, type: 'click', second_open: true });
    } else if (step == 2) {
      handleCloseSuccess();
    } else {
      handleOpenCabinet({
        ...baseData,
        type: 'rejectReopen',
        grid_id: waybillList[0].ec_grid,
        waybill_no,
      });
    }
  };

  const handleConfirm = () => {
    refreshControl(REFRESH_KEY_APPOINTMENT_LIST);
    if (step == 1) {
      handleContinue();
    } else if (step == 2) {
      handleOpenCabinet({
        ...baseData,
        type: 'getRejectionList',
        batch_no,
      });
      // setStep(3);
    } else if (step == 3) {
      handleCloseSuccess();
    }
  };

  const handleOpen = () => {
    const _checked = checked;
    if (waybillList.length == 1) {
      _checked.push(waybillList[0]);
    }
    if (!_checked.length) {
      Taro.kbToast({
        text: '请选择要退回的包裹',
      });
      return;
    }
    handleOpenCabinet({
      ...baseData,
      type: 'rejection',
      rejection_list: _checked,
      grid_id: grid_id || waybillList[0].ec_grid,
      batch_no,
    });
    setChecked([]);
  };

  useEffect(() => {
    if (step == 3 && status) {
      setStep(1);
    }
  }, [step, status, message]);

  useEffect(() => {
    if (Array.isArray(waybillList) && waybillList.length) {
      setStep(3);
    }
  }, [waybillList]);

  useEffect(() => {
    dispatchGet();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const updateRejectionData = (v) => {
    setRejectionInfo((pre) => ({
      ...pre,
      ...v,
    }));
  };

  return {
    visible,
    step,
    checked,
    brands,
    status,
    rejectionInfo,
    setStep,
    handleChoose,
    handleCancel,
    handleConfirm,
    handleOpen,
    handleCloseSuccess,
    cabinetPickupRef,
    updateRejectionData,
  };
};
