/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { openLocation } from '@/components/_pages/store-card/_utils';
import { refreshControl, REFRESH_KEY_CABINET } from '@/utils/refresh-control';
import { makePhoneCall } from '@base/utils/utils';
import { Fragment, Image, View } from '@tarojs/components';
import Taro, { useMemo } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import { transformDakInfo } from '../../pickup/_utils';
import './cabinet.scss';

const CabinetCard = ({ data = {}, lite, showTips }) => {
  const dakInfo = useMemo(() => transformDakInfo(data), [data]);
  const operate = useMemo(() => {
    const arr = [
      {
        key: 1,
        name: '取件码取件',
        value: 'authcode',
      },
      {
        key: 2,
        name: '身份码',
        value: 'idcard',
      },
    ];
    if (dakInfo.cabinet_info && dakInfo.cabinet_info.query_code == 1) {
      arr.push({
        key: 1,
        name: '单号查件',
        value: 'search2',
      });
    }
    return arr;
  }, [dakInfo]);

  const handleCall = (e) => {
    e.stopPropagation();
    const { phone } = dakInfo;
    makePhoneCall(phone);
  };

  const handleOpenAddr = () => {
    openLocation(dakInfo);
  };

  const handleNavigator = (key) => {
    const { dakId, name } = dakInfo;
    refreshControl(REFRESH_KEY_CABINET);
    switch (key) {
      case 'idcard':
        Taro.navigator({
          url: 'IDcode',
          options: {
            action: 'turnstiles',
          },
        });
        break;
      case 'authcode':
        Taro.navigator({
          url: 'pickup/code',
          options: {
            data: JSON.stringify(dakInfo),
          },
        });
        break;
      case 'search2':
        Taro.navigator({
          url: 'query/match',
          options: {
            position: 'appointment',
            dakId,
            name,
            isCabinet: 1,
          },
        });
        break;
    }
  };

  return (
    <View className='kb-cabinetCard'>
      <View className='kb-cabinetCard_wrap'>
        <View className='at-row at-row__align--between kb-spacing-sm-tb'>
          <View>
            <Image
              className='kb-cabinetCard_icon'
              src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_cabinet.png?v=20240712'
            />
          </View>
          <View>
            {dakInfo.name && (
              <View className='kb-size__xl' hoverClass='kb-hover-opacity' onClick={handleCall}>
                {dakInfo.name}{' '}
                <AtIcon
                  prefixClass='kb-icon'
                  value='call'
                  className='kb-size__base kb-color__brand'
                />
              </View>
            )}
            {dakInfo.address && (
              <View
                className='kb-size__sm kb-color__grey'
                hoverClass='kb-hover-opacity'
                onClick={handleOpenAddr}
              >
                {dakInfo.address}{' '}
                <AtIcon
                  prefixClass='kb-icon'
                  value='location'
                  className='kb-size__base kb-color__brand'
                />
              </View>
            )}
          </View>
        </View>
        {!lite && (
          <Fragment>
            <View className='kb-line' />
            <View className='at-row at-row__align--center'>
              {operate.map((item) => (
                <View
                  key={item.key}
                  className='kb-text-center kb-spacing-md-t kb-w30'
                  hoverClass='kb-hover-opacity'
                  onClick={() => handleNavigator(item.value)}
                >
                  <View>
                    <AtIcon
                      prefixClass='kb-icon'
                      value={item.value}
                      className='kb-color__brand kb-size__xxl'
                    />
                  </View>
                  <View className='kb-margin-sm-t'>{item.name}</View>
                </View>
              ))}
            </View>
          </Fragment>
        )}
      </View>
      {showTips && <View className='kb-tips'>确定您在快递柜前再开柜取件，避免包裹丢失</View>}
    </View>
  );
};

CabinetCard.options = {
  addGlobalClass: true,
};

export default CabinetCard;
